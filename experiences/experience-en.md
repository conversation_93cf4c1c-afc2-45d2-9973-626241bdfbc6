# Work Experience

## Baidu Systems Department - Senior Software Engineer

### Project: GTC - Global Traffic Scheduling System

- Responsible for the development and iteration of the GTC, responsible for multiple modules and design and development, implemented new scheduling strategies, helped the business line save nearly 1 million yuan in bandwidth costs every month.
- Designed and implemented an automated health check task deployment module to automate the entire domain detection process, resolving the previous issues of complex and error-prone configurations.
- Implemented a circuit breaker to enhance the availability of the outgoing service system.
- Managed a portion of front-end work while actively participating in recruitment and mentoring of new team members.
- Responsible for code review of new colleagues in the group, and responsible for writing and maintaining the team's technical documentation.

### Project: HTTPDNS

Took over the company's HTTPDNS service and spearheaded the development efforts, making substantial improvements to the system:

- Enhanced Availability: Upgraded and revamped the architecture to eliminate the risk of single-point of failure in the system. Restructured the domain resolution process, reducing MTTR from the original **over 10 minutes** to **within 5 minutes**.
- Optimized performance: Used techniques such as pprof and benchmark testing to analyze system performance bottlenecks. Implemented optimizations like pooling (Pool) and buffering (Buffer), resulting in approximately **40%** reduction in CPU utilization, about **35%** reduction in latency, and reducing machine costs by roughly **50%**.
- Improved Timeliness: Designed and implemented a distributed configuration delivery system based on Golang. This system took control of configuration management and dispatchment for the HTTPDNS system, enhancing configuration dispatchment speed from the previous **5-10 minutes** to around **20 seconds**.
- Cost Reduction: Conducted a comprehensive analysis of the system's overall costs and executed multiple optimization methods, including implementing BGP network-to-static network conversion. These measures led to a reduction of approximately **40%** in the unit cost of HTTPDNS requests.
- Containerization Refactoring: Migrated the entire legacy system to the K8S container platform, resolving issues associated with maintaining multiple stateful modules and facilitating scalability.

### Project: Large Language Models (LLM) Application

- Developed the AI Coder Bot. Generated backend and frontend code based on requirements, significantly improving development efficiency.
- Utilized technology stack: Python, Langchain framework, vector databases, and ChatGPT.

### Project: Large Language Models (LLM) Platform Development

- Conducted secondary development of a Discord-like project, constructing an internal large model platform. This platform now supports over 300 large model applications.
- Led backend development efforts, employing a technology stack that includes Rust, Python, and the Langchain framework.

## ByteDance Engineering Architecture Department - Backend Engineer

### Project: E-commerce Logistics System

- Participated in the extensive refactoring of a large-scale logistics system, transforming it into a microservices architecture. This addressed issues related to system bloat and maintenance.
- Designed and implemented a Golang-based logistics scheduler responsible for dispatching logistics query tasks and updating logistics status in real-time, serving the entire company's logistics queries.
- Employed algorithms and data structures to solve practical problems, including the development of a diff tool to address complex model update issues, resulting in increased efficiency.
- Developed a prefix tree component to resolve sluggish query speeds when interacting with telecom operators in the frontend.

## Other Projects and Research Experience

- During the university, participated in the Finders student team, developing an interest-based social application. Responsible for designing the entire product's backend architecture and database model. Employed technologies like WebSockets and message queues to implement an IM system based on write-diffusion model. The project received sponsorships from the school and Alibaba Cloud.
- Enthusiastic about blockchain-related technologies, familiar with Bitcoin, Ethereum, and their underlying principles. Experimented with Ethereum smart contract development, gained knowledge of common ERC protocols, and acquired proficiency in consensus mechanisms such as PoW and PoS. Attempted to develop Ethereum DApps.
