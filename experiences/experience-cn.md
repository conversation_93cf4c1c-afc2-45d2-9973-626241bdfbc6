# 工作经历

## 百度系统部 - 后端开发工程师

### 项目：GTC(Global Traffic Control) 全局流量调度系统

- 负责 GTC 的开发和迭代工作，实现新的调度策略，节省每月将近100多万元的带宽成本
- 设计并实现新系统模块，自动化域名探测，消除配置繁琐性和容易遗漏的问题
- 实现熔断器，提升接出服务系统的可用性
- 负责新成员的代码review，进行技术串讲，维护团队技术文档

### 项目：HTTPDNS

- 接手 HTTPDNS 服务并担任研发负责人，引领系统大规模升级改造
- 架构升级消除系统单点故障风险，并重构域名解析链路，将 MTTR 时间从原先的**10分钟以上**降低到了**5分钟以内**
- 利用 pprof 和基准测试等工具分析系统性能瓶颈，并通过 Pool 和 Buffer 等优化手段，将 CPU 使用率降低约**40%**，延时降低约**35%**，机器成本减少约**50%**
- 设计并实现基于 Golang 的分布式配置下发系统，接管 HTTPDNS 系统的配置管理和下发工作，将配置下发时效由原先**5-10分钟**降低到了**20s**左右
- 分析整个系统成本，引入多项优化措施，如 BGP 网络转静态网络等功能，降低 HTTPDNS 请求的单位成本约**40%**
- 进行容器化重构，将整个老系统迁移到 K8S 容器平台上，解决多个有状态模块的维护和扩容问题

### 项目：大模型（LLM）应用

- 开发 AI Coder 大模型应用，根据需求文档生成后端和前端代码，提高开发效率
- 使用技术栈：Python、Langchain框架、向量数据库和ChatGPT

### 项目：大模型平台的开发

- 对于开源项目进行二次开发，构建内部大模型 Bot 应用开发平台，已成功支持300多个大模型应用接入
- 主导后端开发工作，使用技术栈：Rust、Python和Langchain框架

## 字节跳动工程架构部 - 后端开发工程师

### 项目：电商物流系统

- 参与大型物流系统的重构，将系统改造为微服务架构，成功解决系统肿胀和维护问题
- 设计和实现了 Golang 物流调度器，负责调度物流查询任务，实时更新物流状态，服务整个公司的物流查询需求
- 运用算法和数据结构解决实际问题，其中包括开发 diff 工具，用于解决多个复杂模型更新问题，大幅提升效率
- 开发前缀树组件以解决前端查询运营商速度慢的问题

## 其他项目与研究经历

- 大学期间参与Finders学生团队，开发了基于兴趣的社交应用，负责设计整个产品后端的架构和数据库模型设计，使用websocket、消息队列等技术设计并实现了基于写扩散模式的IM系统，项目当时获得了学校以及阿里云的赞助
- 对区块链相关技术有兴趣，熟悉Bitcoin，Ethereum 等项目以及了解底层原理，业余尝试写过 Ethereum 智能合约，了解常见的ERC协议，熟悉常见共识机制如PoW，PoS等，尝试开发过以太坊DApp
