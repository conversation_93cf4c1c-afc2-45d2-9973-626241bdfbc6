## Software Developer [Golang]

$65.00/hr
Hello! Cbrom here.
I am an experienced software developer mainly interested in:
✔ backend/API
✔ system architecture
✔ system development
✔ CLI tools and scripts

I consider myself to be an innovative and hard-working individual who can achieve anything. I am always exploring new skills and ready for any challenge.

## Golang developer

$50.00/hr
I have been working as a Golang developer for 3 years in Tradingview (the most popular portal for traders) and Exmo (cryptocurrency exchange) up to now. In general, I was developing highload applications, services with not trivial business logic, daemons with high concurrency, very wide-range of things...
I'm interested in any relevant projects as part-time developer. My motivation here is to make something different to my current job projects and to increase my income. I have java and cpp experience (in astrophysics project when I was studying at the University), real experience in Java for a year while I was working in Tradingview

## GoLang / Node.js / React Developer

$50.00/hr
I am a full-stack developer with experience in building web applications. I specialize in Golang and Node and am very interested in them. And I have professional experience in PostgreSQL, gRPC, RabbitMQ, Restful API, GraphQL, and AWS.

I have five years of experience designing, planning, and implementing micro service-oriented systems, CLI tools, and internal and external APIs. Especially in microservices development to develop a complete application consisting of segments dedicated to frontend communication and processing, analytics, and telemetry.

While I have good hands in backend development, I know some different frontend frameworks like React, Next, and Vue. I have vital innovation, problem-solving, and teamwork skills acquired from my background in various industries and scenarios.

Please send me a message, and I'll be happy to discuss it further.

## Full Stack Developer | React | Vue | Node.js | Golang

$75.00/hr
👋 I'm a full-stack developer with over 10+years of experience in building high-quality web applications for clients worldwide. I deliver clean, scalable and maintainable code. Good UX & project management skills. I focus on business needs first. I work with clients of any scale: from startups & small teams to enterprise.

Great experience in Automotive | eCommerce | Retail | Fin-tech businesses

♣︎ I offer:
👉🏼 Web application development
👉🏼 API development / Integrations / Automations
👉🏼 Frontend/backend development

♣︎ Tech stack:
Front: React / Vue / Svelte / Next.js / Nuxt.js / Redux / React Native / React Query / typescript
Back: Node.js / Express / Golang
CD: Docker / Kubernetes / Helm / Ansible / AWS / GCP
Databases: Postgres / Mongo / Cockroach.db / MySQL / Firebase
Message brokers: Kafka / NATS / RabbitMQ
APIs: gRPC / REST / graphql
Patterns: Hexagonal architecture / CQRS / Event sourcing & more

Keywords: javascript, kubernetes, docusign, no-code tools, integration, REST API, e-commerce, shopify, stripe, serverless, cloud functions, sharetribe, marketplace

Let's get in touch!

## GoLang | GoKit | Microservices Architecture | Java | PostgreSQL

$25.00/hr
● Back-end Developer with 4+ years of experience in GoLang and Java.
● Having a Master's Degree in Computer Applications.
● Built web applications for large-scale enterprises that are scalable, reliable, & secure at the same time.
● Experience with agile project management principles.
● Backed by an expert team of tech-savvy developers, designers, and creative engineers.

➡ TECHNICAL SKILLS:

● PROGRAMMING LANGUAGES: Java, Golang, JavaScript, Python

● DATABASES: PostgreSQL, MySQL, Firebase

● DEVELOPMENT ENVIRONMENTS: Visual Studio Code, IntelliJ Idea, MySQL Workbench, PGAdmin

● SOFTWARE PATTERNS: Microservice, MVC

● SERVICES: Github, Gitlab (Version Control), Docker (Containerization), AWS S3 Bucket (Storage Service), AWS Cognito, Keycloak (Authentication Control)

➡ Industry Experience: Banking & Finance || Manufacturing || E-commerce || Energy || Food || Travel

## Sr. Golang Developer

$110.00/hr
Experienced Full Stack Developer/devOPS with a demonstrated history
of working in the financial services and Public sector. Strong
engineering professional skilled in GO, React, Vue, PHP, Docker, Kubernetes, Ansible, Elastic,
Python and API Development.

## backend developer, prefered golang, scala, python, nodejs

$57.00/hr
I love coding, exploring new techs and diving deep into thing’s internal.

- Programming languages: Golang, Scala, Python
- Big Data frameworks: Apache Spark, Apache Hadoop, Apache Kafka
- Linux system administration
- Building and deploying application with Docker
- Source control management: Git
- Databases: ClickHouse, Redis, Mysql

## Senior Golang Developer

$90.00/hr
My area of expertise covers the followings:

- Frontend/Backend development
- Software architecture
- Database design
- Network administration

Software Development skills:
Languages:
• Expert: GoLang, PHP
• Middle: JavaScript (React, jQuery) CSS (Sass), HTML, Shell Script
• Junior: C++, Python, Perl
Technologies:
• PostgreSQL, MySQL, SQLite, Redis
• RESTfulAPI, GraphQL, gRPC, ProtoBuf, MQTT, AMQP
• Git, CI/CD, AWS, Docker

Networking Administration skills:
OS: Linux (Fedora, Ubuntu), FreeBSD, MacOS, Cisco IOS (Router, Switch)
Technologies:
• VLAN, OSPF, BGP, IPsec, Radius, LDAP, Kerberos, DNS, DHCP, HTTP
• Ansible, FreeIPA
• Raid 0,1,5,10, IPMI (iDRAC)
• Dual Stack Network, High Availability

Environment:

- MacOS
- Emacs

Over the last 14 years I were involved in a wide range of projects using different technology stacks. In most cases I was involved in the entire life cycle of each project starting with business requirements and ending with application design and implementation.
I have strong analytical/management skills and I'm always interested to get involved in challenging projects.

## CTO / GoLang / ReactJS / React Native / PHP / Full Stack Developer

$100.00/hr
I'm a passionate software developer with more than 14 years of experience in commercial software development and various problem areas. I focus my effort on creating high-performance, low-resource consumption, and cost-effective solutions tailored to each project.

I'm looking for projects where I can add value as a software architect and engineer with the opportunity for long-term productive collaboration.

Currently have experience working with GoLang, Node.js, JavaScript, ReactJS, PHP, Laravel, WordPress, MySQL, PostgreSQL, MongoDB, gRPC, Kafka, Redis, LESS, ElasticSearch, OpenAi, ChatGPT, as well can administrate systems on Kubernetes, Docker, Linux.

I offer a risk-free trial to each customer, just ask.

## my own

About me:

👋 I'm a Golang developer with 3+ years of experience at big tech companies Baidu Inc. and ByteDance(TikTok), I'm proficient in using Golang to develop high-performance, highly available, and scalable systems. Able to independently design and implement a highly available microservice architecture, and use Golang's built-in concurrent mechanisms to improve the overall system throughput.

I consider myself to be an innovative and hard-working individual who can achieve anything. I am always exploring new skills and ready for any challenge. And I am here for becoming an excellent freelancer.

♣︎ I offer:
✔ backend/API/frontent
✔ system architecture
✔ system development
✔ system optimization
✔ CLI tools and scripts
✔ ready for any challenge !

🛠 Professional Skills:

● Programming languages: Proficient in Golang, and Python, and have experience in TypeScript
● Backend: Skilled in using Golang to develop high-performance, highly available, and scalable systems. Able to independently design and implement a highly available microservice architecture, and use Golang's built-in concurrent mechanisms to improve the overall system throughput
● Full stack: Have a comprehensive understanding of the technical principles from back-end to front-end, familiar with REST API and GraphQL. Understand front-end related technology stacks, such as React, Next.js, and Tailwind css, and have related full-stack practical experience
● Fundamentals: Strong computer theoretical foundation, including algorithms, data structures, and OS. Pays attention to code style and quality, and emphasize unit test coverage. Have good coding habits and documentation writing ability
● Networking: Strong computer networking foundation, familiar with common network protocols such as HTTP/DNS/TCP/UDP/IP, and understands common network models
● Databases: PostgreSQL / Redis / MySQL
● Message brokers: Kafka
● APIs: gRPC / REST / GraphQL
● CD: Docker / Docker-Compose

👨🏻‍💻 Past Work Experience:

● As the R&D manager of the HTTPDNS project, responsible for the availability optimization of the HTTPDNS system, as well as performance optimization, etc. Upgraded the system architecture to a master-slave architecture, solving the single-point failure risk of the original system. Re-designed the domain name resolution link, reducing the MTTR time from more than 10 minutes to within 5 minutes.
● Designed and implemented a Golang-based distributed configuration delivery system, which took over the configuration management and delivery work of the HTTPDNS system, solved the historical problems of difficult configuration management, slow update timeliness, and low availability, and reduced the delivery timeliness from 5-10 minutes to around 20s.
● Analyzed system performance problems using pprof, benchmark tests, etc., and optimized system CPU usage by nearly 40% and delay by around 30% through methods such as Pool and Buffer, reducing machine use costs by around 50%.
● Designed and implemented the logistics scheduler using Golang, which can schedule logistics query tasks and update logistics status in real-time, and the system took over the company's entire logistics query.
● Participated in the Finders student team during college and developed a social app based on interests. I was responsible for the design of the entire product back-end architecture and database model design. Used technologies such as websocket and message queue to design and implement an IM system based on write diffusion model, and the project received sponsorship from the school and Alibaba Cloud at that time.

Please send me a message, and I'll be happy to discuss it further.
