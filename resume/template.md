<div style="position:relative;">
  <img src="https://i.ibb.co/JkkDnhD/0-K7-A2889.jpg" alt="Your Name" style="width:110px;height:auto;position:absolute; top:0; right:0;">
</div>

<center>
<div style="padding-top:5px">
  <p style="font-weight: bold;font-size: 1.5em;">艾力亚尔 Elyar</p>
  <div style="line-height:2px;font-weight:500;"> 
  <p>+86 15522005019 | <EMAIL></p>
  <p>github.com/psychopurp</p>
  </div>
</div>
</center>



<div style="line-height:10px">
<h2>👨🏻‍🎓 教育背景</h2>
<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >南开大学</p>
    <p style="font-weight: bold;font-size: 1.17em;">2017年08月 - 2021年06月</p>
</div>
<p>计算机科学与技术 本科</p>
</div>


## 🛠 专业技能

- 编程语言：熟练掌握 Golang，Python，有 TypeScript 相关编程经验
- 基础：有扎实的计算机理论基础，包括算法，数据结构，OS等。有代码洁癖，注重代码的风格和质量，注重单测覆盖率，有良好的代码编程习惯及文档编写能力，能流畅阅读英文文档
- 后端：擅长使用 Golang 进行高性能、高可用性和高可扩展性的系统开发。能够独立设计和实现高可用的微服务架构，并且能够使用 Golang 内置的并发机制，提高系统的整体吞吐量
- 网络：扎实的计算机网络基础，熟悉常见的网络协议如：HTTP/DNS/TCP/UDP/IP，了解常见的网络模型
- 全栈：对后端到前端的技术原理有全面的认识，熟悉 REST API 和 GraphQL。了解前端相关技术栈，如 React，Next.js 和 Tailwind css，具有相关全栈实践经验
- 其他：`MySQL` `Redis` `SQLite` `Git` `Linux` `docker` `docker-compose` 
- 证书：英语六级（CET-6）


## 👨🏻‍💻 工作经历

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >XXX-  Golang 研发工程师</p>
    <p style="font-weight: bold;font-size: 1.17em;">2021年06月 - 至今</p>
</div>

- 负责 GTC 全局流量调度系统的开发和迭代工作，负责了多个模块和设计和开发，实现了新的调度策略，帮助了业务线每个月节省将近100多万元的带宽成本
- 作为 HTTPDNS 项目的研发负责人，负责 HTTPDNS 系统的可用性优化，以及性能优化等工作。对系统架构进行了主从架构的升级，解决了原先的系统单点故障风险。重构了域名解析链路，将 MTTR 时间从原先的**10分钟以上**降低到了**5分钟以内**
- 设计并实现了基于 Golang 的分布式配置下发系统，该系统接管了 HTTPDNS 系统的配置管理和下发工作，解决了原先配置难以统一管理，更新时效慢，可用性较低等历史问题，将下发时效性由原先**5-10分钟**降低到了**20s**左右
- 使用 pprof，benchmark test 等方式分析系统性能问题，并通过 Pool，Buffer 等方式优化了系统将近  **30%** 的CPU使用率，**10%** 左右的延时，将机器使用成本降低了 **50%** 左右
- 熟悉前端 React 技术栈，负责了团队内部系统的部分前端工作，以及引入人才等事项
- 负责组内新同学的代码review，以及串讲等工作，负责团队技术文档的编写以及维护


<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >XXX -  Golang 研发工程师</p>
    <p style="font-weight: bold;font-size: 1.17em;">2020年01月 - 2021年02月</p>
</div>

- 参与物流系统的重构，将系统改造成了微服务架构，解决了原先系统肿胀，难以维护迭代的现状
- 使用 Golang 设计并实现了物流调度器，能够调度物流查询任务，实时更新物流状态，该系统承接了整个公司的物流查询
- 使用一些算法和数据结构解决实际问题，如开发了diff工具，解决了当时的多个复杂模型更新的问题，通过对不同模型做diff，对实际变更的地方做更新，显著提升了效率。开发了前缀树组件解决了前端查询运营商速度慢的问题等


## 💡 其他项目与研究经历

- 在大学期间参与Finders学生团队，开发了一款基于兴趣的社交应用，当时负责设计了整个产品后端的架构设计，以及数据库模型设计。使用websocket、消息队列等技术设计并实现了基于写扩散模式的IM系统，项目当时获得了学校以及阿里云的赞助
- 对区块链相关技术有兴趣，熟悉Bitcoin，Ethereum 等项目以及了解底层原理，业余尝试写过 Ethereum 智能合约，了解常见的ERC协议，熟悉常见共识机制如PoW，PoS等，尝试开发过以太坊DApp
- 对前端技术也有兴趣，有空的时候会研究一下，熟悉react，tailwindcss 等技术栈
