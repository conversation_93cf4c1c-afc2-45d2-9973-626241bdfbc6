<div style="position:relative;">
  <img src="https://i.ibb.co/JkkDnhD/0-K7-A2889.jpg" alt="Elyar" style="width:110px;height:auto;position:absolute; top:0; right:0;">
</div>

<center>
<div style="padding-top:5px">
  <p style="font-weight: bold;font-size: 1.5em;">艾力亚尔 Elyar</p>
  <div style="line-height:2px;font-weight:500;">
  <p>+86 15522005019 | <EMAIL></p>
  <p>github.com/psychopurp | 区块链/Web3开发工程师</p>
  </div>
</div>
</center>

## 🎯 职业目标

具有3年+大规模分布式系统开发经验的软件工程师，专注于区块链基础设施和DeFi协议开发。在百度和字节跳动积累了丰富的高并发、高可用系统设计经验，熟悉区块链底层原理，致力于构建下一代去中心化金融基础设施。

<div style="line-height:10px">
<h2>👨🏻‍🎓 教育背景</h2>
<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >南开大学</p>
    <p style="font-weight: bold;font-size: 1.17em;">2017年08月 - 2021年06月</p>
</div>
<p>计算机科学与技术 本科</p>
</div>

## 🛠 核心技能

### 区块链技术栈
- **智能合约开发**: Solidity、以太坊DApp开发、ERC标准协议
- **区块链原理**: Bitcoin、Ethereum底层机制，PoW/PoS共识算法
- **Layer 2方案**: 了解Rollups、状态通道、侧链技术
- **DeFi协议**: AMM、借贷协议、流动性挖矿机制

### 分布式系统专长
- **高并发架构**: 设计支持大规模用户的分布式系统
- **性能优化**: 系统性能调优，成本优化（降低40-50%）
- **微服务架构**: 服务拆分、容器化部署、K8S管理
- **网络协议**: P2P网络、负载均衡、流量调度

### 编程技术
- **主要语言**: Golang（3年+）、Python、Solidity
- **辅助语言**: Rust、TypeScript、JavaScript
- **数据存储**: MySQL、MongoDB、Redis、区块链状态存储
- **云原生**: Docker、Kubernetes、AWS/GCP/阿里云
- **开发工具**: Git、CI/CD、监控告警、性能分析

## 👨🏻‍💻 工作经历

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >百度系统部（BFE团队）- 高级后端工程师</p>
    <p style="font-weight: bold;font-size: 1.17em;">2021年06月 - 至今</p>
</div>

**🌐 全局流量调度系统 (GTC) - 类似区块链网络路由优化**
- 设计实现智能流量调度算法，**月节省带宽成本100万+**，展现了对网络效率优化的深度理解
- 开发自动化域名探测系统，消除人工配置错误，类似区块链网络的自动发现机制
- 实现熔断器模式提升系统可用性，为构建高可用DeFi协议奠定基础
- 负责团队技术培训和代码审查，具备技术领导能力

**🔗 分布式DNS服务 (HTTPDNS) - 去中心化基础设施经验**
- 担任系统架构负责人，**消除单点故障**，将MTTR从10分钟降至5分钟内
- 通过性能分析和优化，**CPU使用率降低40%，延时降低35%，成本减少50%**
- 设计分布式配置系统，配置下发时效从5-10分钟优化至20秒，展现了分布式一致性处理能力
- 主导容器化迁移，解决有状态服务的扩容问题，为区块链节点部署提供经验

**🤖 AI/大模型平台开发 - 智能合约自动化思维**
- 开发AI代码生成工具，提升开发效率，展现了对自动化和智能化的理解
- 构建支持300+应用的大模型平台，具备大规模系统集成经验
- 使用Rust、Python技术栈，为多链开发奠定语言基础

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >字节跳动工程架构部 - 后端开发工程师</p>
    <p style="font-weight: bold;font-size: 1.17em;">2020年01月 - 2021年02月</p>
</div>

**📦 电商物流系统 - 供应链区块链应用基础**
- 参与大型系统微服务化重构，解决系统复杂性问题，为模块化区块链架构提供经验
- 设计Golang物流调度器，处理实时状态更新，类似区块链状态机制
- 开发算法优化工具，包括diff算法和前缀树，展现了算法工程化能力
- 服务全公司物流查询需求，具备大规模系统设计经验

## 🚀 区块链项目经验

**🔗 以太坊DApp开发**
- 使用Solidity开发智能合约，熟悉ERC-20/721/1155等标准协议
- 构建完整的前后端DApp应用，包括Web3.js集成和MetaMask交互
- 了解Gas优化、安全审计、合约升级等最佳实践

**📚 区块链技术研究**
- 深入研究Bitcoin和Ethereum底层原理，包括UTXO模型、账户模型
- 理解PoW、PoS等共识机制的设计权衡和实现细节
- 关注DeFi生态发展，研究AMM、借贷、衍生品等协议设计

## 💡 其他项目经历

**📱 社交应用开发 (大学项目)**
- 设计基于兴趣的社交平台后端架构，获得学校和阿里云赞助
- 实现基于WebSocket的IM系统，采用写扩散模式，为P2P通信提供经验
- 负责数据库设计和消息队列架构，展现了系统设计能力

## 🎖 技术亮点

- **性能优化专家**: 多个项目实现40-50%的性能提升和成本降低
- **分布式系统架构师**: 具备大规模、高可用系统设计经验
- **全栈开发能力**: 从底层系统到前端应用的完整技术栈
- **快速学习能力**: 从传统后端快速扩展到AI和区块链领域
- **团队协作领导**: 具备代码审查、技术培训、项目管理经验

## 🌟 求职意向

**目标职位**: 区块链开发工程师 / DeFi协议开发 / Web3基础设施工程师
**期望方向**: Layer 1/2协议开发、DeFi协议设计、跨链基础设施、企业区块链解决方案
**工作地点**: 北京/上海/深圳/新加坡/远程工作
