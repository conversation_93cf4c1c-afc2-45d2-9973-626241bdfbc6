<div style="position:relative;">
  <img src="https://i.ibb.co/JkkDnhD/0-K7-A2889.jpg" alt="Your Name" style="width:110px;height:auto;position:absolute; top:0; right:0;">
</div>

<center>
<div style="padding-top:5px">
  <p style="font-weight: bold;font-size: 1.5em;">艾力亚尔 Elyar</p>
  <div style="line-height:2px;font-weight:500;">
  <p>+86 15522005019 | <EMAIL></p>
  <p>github.com/psychopurp</p>
  </div>
</div>
</center>

<div style="line-height:10px">
<h2>👨🏻‍🎓 教育背景</h2>
<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >南开大学</p>
    <p style="font-weight: bold;font-size: 1.17em;">2017年08月 - 2021年06月</p>
</div>
<p>计算机科学与技术 本科</p>
</div>

## 🛠 专业技能

- **编程语言**: 熟练掌握 Golang、Python，有 Rust、NodeJS 和 TypeScript 相关编程经验
- **分布式系统**: 大规模分布式系统的设计、开发和优化
- **数据存储**: MySQL、MongoDB、Redis、S3、TSDB等数据存储
- **云计算**: GCP、AWS、阿里云和百度云，具备云原生应用程序开发经验
- **日志和搜索**: Elastic Search、ELK Stack，日志管理和可视化
- **数据流和消息队列**: Kafka和其他消息队列技术，实时数据流处理
- **容器**: Docker、Kubernetes等容器技术
- **前端**: React、Next.js、Tailwind CSS，全栈实践经验
- **团队协作**: 跨职能团队协作和领导

## 👨🏻‍💻 工作经历

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >百度系统部（BFE团队）-  Golang 研发工程师</p>
    <p style="font-weight: bold;font-size: 1.17em;">2021年06月 - 至今</p>
</div>

**项目：GTC(Global Traffic Control) 全局流量调度系统**

- 负责 GTC 的开发和迭代工作，实现新的调度策略，节省每月将近100多万元的带宽成本
- 设计并实现新系统模块，自动化域名探测，消除配置繁琐性和容易遗漏的问题
- 实现熔断器，提升接出服务系统的可用性
- 负责新成员的代码review，进行技术串讲，维护团队技术文档

**项目：HTTPDNS**

- 接手 HTTPDNS 服务并担任研发负责人，引领系统大规模升级改造
- 架构升级消除系统单点故障风险，并重构域名解析链路，将 MTTR 时间从原先的**10分钟以上**降低到了**5分钟以内**
- 利用 pprof 和基准测试等工具分析系统性能瓶颈，并通过 Pool 和 Buffer 等优化手段，将 CPU 使用率降低约**40%**，延时降低约**35%**，机器成本减少约**50%**
- 设计并实现基于 Golang 的分布式配置下发系统，接管 HTTPDNS 系统的配置管理和下发工作，将配置下发时效由原先**5-10分钟**降低到了**20s**左右
- 分析整个系统成本，引入多项优化措施，如 BGP 网络转静态网络等功能，降低 HTTPDNS 请求的单位成本约**40%**
- 进行容器化重构，将整个老系统迁移到 K8S 容器平台上，解决多个有状态模块的维护和扩容问题

**项目：大模型（LLM）应用**

- 开发 AI Coder 大模型应用，根据需求文档生成后端和前端代码，提高开发效率
- 使用技术栈：Python、Langchain框架、向量数据库和ChatGPT

**项目：大模型平台的开发**

- 对于开源项目进行二次开发，构建内部大模型 Bot 应用开发平台，已成功支持300多个大模型应用接入
- 主导后端开发工作，使用技术栈：Rust、Python和Langchain框架

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >字节跳动工程架构部 -  Golang 研发工程师</p>
    <p style="font-weight: bold;font-size: 1.17em;">2020年01月 - 2021年02月</p>
</div>

**项目：电商物流系统**

- 参与大型物流系统的重构，将系统改造为微服务架构，成功解决系统肿胀和维护问题
- 设计和实现了 Golang 物流调度器，负责调度物流查询任务，实时更新物流状态，服务整个公司的物流查询需求
- 运用算法和数据结构解决实际问题，其中包括开发 diff 工具，用于解决多个复杂模型更新问题，大幅提升效率
- 开发前缀树组件以解决前端查询运营商速度慢的问题

## 💡 其他项目与研究经历

- 大学期间参与Finders学生团队，开发了基于兴趣的社交应用，负责设计整个产品后端的架构和数据库模型设计，使用websocket、消息队列等技术设计并实现了基于写扩散模式的IM系统，项目当时获得了学校以及阿里云的赞助
