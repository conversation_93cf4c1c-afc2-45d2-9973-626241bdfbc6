<div style="position:relative;">
  <img src="https://i.ibb.co/JkkDnhD/0-K7-A2889.jpg" alt="Elyar" style="width:110px;height:auto;position:absolute; top:0; right:0;">
</div>

<center>
<div style="padding-top:5px">
  <p style="font-weight: bold;font-size: 1.5em;"><PERSON><PERSON> (艾力亚尔)</p>
  <div style="line-height:2px;font-weight:500;">
  <p>+86 15522005019 | <EMAIL></p>
  <p>github.com/psychopurp | Blockchain/Web3 Engineer</p>
  </div>
</div>
</center>

## 🎯 Professional Summary

Experienced software engineer with 3+ years in large-scale distributed systems, transitioning to blockchain infrastructure and DeFi protocol development. Proven track record at Baidu and ByteDance building high-concurrency, high-availability systems. Deep understanding of blockchain fundamentals with hands-on smart contract development experience.

<div style="line-height:10px">
<h2>👨🏻‍🎓 Education</h2>
<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >Nankai University</p>
    <p style="font-weight: bold;font-size: 1.17em;">Aug 2017 - Jun 2021</p>
</div>
<p>Bachelor of Computer Science and Technology</p>
</div>

## 🛠 Core Skills

### Blockchain Technology Stack
- **Smart Contract Development**: Solidity, Ethereum DApp development, ERC standards
- **Blockchain Fundamentals**: Bitcoin, Ethereum internals, PoW/PoS consensus mechanisms
- **Layer 2 Solutions**: Understanding of Rollups, state channels, sidechains
- **DeFi Protocols**: AMM, lending protocols, liquidity mining mechanisms

### Distributed Systems Expertise
- **High-Concurrency Architecture**: Designing systems for massive scale
- **Performance Optimization**: System tuning, cost reduction (40-50% improvements)
- **Microservices Architecture**: Service decomposition, containerization, K8S management
- **Network Protocols**: P2P networks, load balancing, traffic scheduling

### Programming & Infrastructure
- **Primary Languages**: Golang (3+ years), Python, Solidity
- **Secondary Languages**: Rust, TypeScript, JavaScript
- **Data Storage**: MySQL, MongoDB, Redis, blockchain state storage
- **Cloud Native**: Docker, Kubernetes, AWS/GCP/Alibaba Cloud
- **DevOps**: Git, CI/CD, monitoring, performance profiling

## 👨🏻‍💻 Professional Experience

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >Baidu System Department (BFE Team) - Senior Backend Engineer</p>
    <p style="font-weight: bold;font-size: 1.17em;">Jun 2021 - Present</p>
</div>

**🌐 Global Traffic Control (GTC) System - Blockchain Network Optimization Parallel**
- Designed intelligent traffic scheduling algorithms, **saving 1M+ RMB monthly bandwidth costs**
- Developed automated domain detection system, eliminating manual configuration errors
- Implemented circuit breaker patterns for high availability, foundational for robust DeFi protocols
- Led technical training and code reviews, demonstrating technical leadership capabilities

**🔗 Distributed DNS Service (HTTPDNS) - Decentralized Infrastructure Experience**
- Served as system architecture lead, **eliminated single points of failure**, reduced MTTR from 10+ minutes to <5 minutes
- Achieved **40% CPU reduction, 35% latency improvement, 50% cost savings** through performance optimization
- Designed distributed configuration system, reducing deployment time from 5-10 minutes to ~20 seconds
- Led containerization migration, solving stateful service scaling challenges

**🤖 AI/LLM Platform Development - Smart Contract Automation Mindset**
- Built AI code generation tools, improving development efficiency
- Constructed platform supporting 300+ LLM applications, demonstrating large-scale system integration
- Utilized Rust and Python tech stack, building foundation for multi-chain development

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >ByteDance Engineering Architecture - Backend Engineer</p>
    <p style="font-weight: bold;font-size: 1.17em;">Jan 2020 - Feb 2021</p>
</div>

**📦 E-commerce Logistics System - Supply Chain Blockchain Foundation**
- Participated in large-scale system microservices refactoring, providing experience for modular blockchain architecture
- Designed Golang logistics scheduler for real-time status updates, similar to blockchain state mechanisms
- Developed algorithmic optimization tools including diff algorithms and prefix trees
- Served company-wide logistics query requirements, demonstrating large-scale system design experience

## 🚀 Blockchain Project Experience

**🔗 Ethereum DApp Development**
- Developed smart contracts using Solidity, familiar with ERC-20/721/1155 standards
- Built complete full-stack DApp applications with Web3.js integration and MetaMask interaction
- Understanding of gas optimization, security auditing, and contract upgrade best practices

**📚 Blockchain Technology Research**
- Deep research into Bitcoin and Ethereum fundamentals, including UTXO and account models
- Understanding of consensus mechanism design trade-offs and implementation details (PoW, PoS)
- Following DeFi ecosystem development, studying AMM, lending, and derivatives protocol designs

## 💡 Additional Projects

**📱 Social Application Development (University Project)**
- Designed interest-based social platform backend architecture, sponsored by university and Alibaba Cloud
- Implemented WebSocket-based IM system using write-fanout pattern, providing P2P communication experience
- Responsible for database design and message queue architecture, demonstrating system design capabilities

## 🎖 Technical Highlights

- **Performance Optimization Expert**: Achieved 40-50% performance improvements across multiple projects
- **Distributed Systems Architect**: Extensive experience in large-scale, high-availability system design
- **Full-Stack Development**: Complete technology stack from low-level systems to frontend applications
- **Rapid Learning Ability**: Quick expansion from traditional backend to AI and blockchain domains
- **Team Leadership**: Experience in code review, technical training, and project management

## 🌟 Career Objectives

**Target Positions**: Blockchain Engineer / DeFi Protocol Developer / Web3 Infrastructure Engineer
**Preferred Directions**: Layer 1/2 protocol development, DeFi protocol design, cross-chain infrastructure, enterprise blockchain solutions
**Location Preferences**: Beijing/Shanghai/Shenzhen/Singapore/Remote Work

## 🔗 Notable Achievements

- **Cost Optimization**: Consistently delivered 40-50% cost reductions across multiple projects
- **System Reliability**: Improved system MTTR from 10+ minutes to <5 minutes
- **Performance Engineering**: Reduced CPU usage by 40% and latency by 35% in production systems
- **Scale Management**: Successfully handled company-wide traffic and logistics requirements
- **Innovation Leadership**: Led AI platform supporting 300+ applications and blockchain research initiatives
