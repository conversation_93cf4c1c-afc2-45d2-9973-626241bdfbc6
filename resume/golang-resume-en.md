<div style="position:relative;">
  <img src="https://i.ibb.co/JkkDnhD/0-K7-A2889.jpg" alt="Your Name" style="width:110px;height:auto;position:absolute; top:0; right:0;">
</div>

<center>
<div style="padding-top:5px">
  <p style="font-weight: bold;font-size: 1.5em;">Elyar·Ablimit</p>
  <div style="line-height:2px;font-weight:500;">
  <p>+86 15522005019 | <EMAIL></p>
  <p>github.com/psychopurp</p>
  </div>
</div>
</center>

<div style="line-height:10px">
<h2>👨🏻‍🎓 Education</h2>
<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >Nankai University</p>
    <p style="font-weight: bold;font-size: 1.17em;">Aug 2017 - Jun 2021</p>
</div>
<p>Bachelor's degree in Computer Science and Technology</p>
</div>

## 🛠 Professional Skills

- Programming languages: Proficient in Golang, and Python, and have experience in Rust and TypeScript
- Fundamentals: Strong computer theoretical foundation, including algorithms, data structures, and OS. Pays attention to code style and quality, and emphasize unit test coverage. Have good coding habits and documentation writing ability, and can fluently read English documentation.
- Backend: Skilled in using Golang to develop high-performance, highly available, and scalable systems. Able to independently design and implement a highly available microservice architecture, and use Golang's built-in concurrent mechanisms to improve the overall system throughput.
- Networking: Strong computer networking foundation, familiar with common network protocols such as HTTP/DNS/TCP/UDP/IP, and understands common network models.
- Full stack: Have a comprehensive understanding of the technical principles from back-end to front-end, familiar with REST API. Understand front-end related technology stacks, such as React, Next.js, and Tailwind css, and have related full-stack practical experience.
- Other: `MySQL` `MongoDB` `Redis` `SQLite` `Git` `Linux` `docker` `docker-compose`

## 👨🏻‍💻 Work Experience

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >BFE Team at Baidu Inc. - Golang R&D Engineer</p>
    <p style="font-weight: bold;font-size: 1.17em;">June 2021 - Present</p>
</div>

**Project: GTC - Global Traffic Scheduling System**

- Responsible for the development and iteration of the GTC, responsible for multiple modules and design and development, implemented new scheduling strategies, helped the business line save nearly 1 million yuan in bandwidth costs every month
- Familiar with the front-end stack like React, responsible for some front-end work in the team's internal system.
- Responsible for code review of new colleagues in the group, and responsible for writing and maintaining the team's technical documentation.

**Project: HTTPDNS**

Took over the company's HTTPDNS service and assumed the role of development lead, initiating substantial improvements to the system:

- Enhanced Availability: Upgraded and revamped the architecture to eliminate the risk of single-point of failure in the system. Refactored the domain resolution pipeline, reducing MTTR (Mean Time To Recovery) from the original **over 10 minutes** to **within 5 minutes**.

- Optimized Performance: Used techniques such as pprof and benchmark testing to analyze system performance issues. Implemented optimizations like pooling (Pool) and buffering (Buffer), resulting in approximately **40%** reduction in CPU utilization, about **35%** reduction in latency, and reducing machine costs by roughly **50%**.

- Improved Timeliness: Designed and implemented a distributed configuration delivery system based on Golang. This system took control of configuration management and dispatchment for the HTTPDNS system, enhancing configuration dispatchment speed from the previous **5-10 minutes** to around **20 seconds**.

- Containerization Refactoring: Conducted extensive reengineering of the entire legacy system, migrating it to the K8S container platform. This resolved challenges related to maintaining multiple stateful modules and difficulties in rapid scaling.

**Project: Large Language Models (LLM) Application**

- Responsible for the development of a Multi-Agent application named AI Coder. This application generates backend and frontend code based on requirement documents and can modify existing repository code. It significantly boosts development efficiency. The technology stack includes Python, Langchain framework, vector databases, and ChatGPT.

**Project: Large Language Models (LLM) Platform Development**

- Conducted secondary development based on an open-source project similar to Discord, creating an internal LLM platform. Developers can adapt this platform for their own large model applications, with over 200 large model applications already integrated. Responsible for backend development, utilizing Rust, Python, and the Langchain framework.

<div style="display:flex;justify-content:space-between">
    <p style="font-weight: bold;font-size: 1.17em;" >ByteDance Engineering Architecture Department - Golang R&D Engineer</p>
    <p style="font-weight: bold;font-size: 1.17em;">January 2020 - February 2021</p>
</div>

**Project: E-commerce Logistics System**

- Participated in the refactoring of the logistics system, transforming it into a microservices architecture to address issues of system bloat and maintenance challenges.
- Designed and implemented a logistics scheduler using Golang, capable of managing logistics query tasks and updating logistics status in real-time. This system handles logistics queries across the entire company.
- Utilized algorithms and data structures to solve practical problems, such as developing a diff tool to address the challenge of updating multiple complex models. Implemented a prefix tree component to enhance the speed of frontend queries for operators.

## 💡 Other Projects & Research Experience

- Participated in the Finders student team during college and developed a social app based on interests. I was responsible for the design of the entire product back-end architecture and database model design. Used technologies such as websocket and message queue to design and implement an IM system based on write diffusion model, and the project received sponsorship from the school and Alibaba Cloud at that time.
- Interested in blockchain-related technologies, familiar with Bitcoin, Ethereum, etc. and understand the underlying principles. Tried to write Ethereum smart contracts in my spare time, familiar with common ERC protocols, familiar with common consensus mechanisms such as PoW and PoS, and tried to develop Ethereum DApp.
- Also interested in front-end technologies, will study it when I have time, familiar with React, TailwindCSS, etc.
